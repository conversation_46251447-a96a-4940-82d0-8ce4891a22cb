// Utility functions for coordinate calculations and distance checking
const EARTH_RADIUS = 6371e3; // Earth's radius in meters

// Calculate distance between two points using the Haversine formula
function calculateDistance(coord1, coord2) {
    const φ1 = toRadians(coord1.lat);
    const φ2 = toRadians(coord2.lat);
    const Δφ = toRadians(coord2.lat - coord1.lat);
    const Δλ = toRadians(coord2.lng - coord1.lng);

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return EARTH_RADIUS * c;
}

// Convert degrees to radians
function toRadians(degrees) {
    return degrees * Math.PI / 180;
}

// Check if a point is within a circular area
function isWithinRadius(point, center, radius) {
    return calculateDistance(point, center) <= radius;
}

// Check if coordinates are within campus bounds
function isWithinCampus(coordinates, campusBounds) {
    return coordinates.lat >= campusBounds.south &&
           coordinates.lat <= campusBounds.north &&
           coordinates.lng >= campusBounds.west &&
           coordinates.lng <= campusBounds.east;
}

// Get bearing between two points (for avatar rotation)
function getBearing(start, end) {
    const startLat = toRadians(start.lat);
    const endLat = toRadians(end.lat);
    const dLong = toRadians(end.lng - start.lng);

    const y = Math.sin(dLong) * Math.cos(endLat);
    const x = Math.cos(startLat) * Math.sin(endLat) -
             Math.sin(startLat) * Math.cos(endLat) * Math.cos(dLong);
    
    return (toDegrees(Math.atan2(y, x)) + 360) % 360;
}

// Convert radians to degrees
function toDegrees(radians) {
    return radians * 180 / Math.PI;
}

// Export utility functions
export {
    calculateDistance,
    isWithinRadius,
    isWithinCampus,
    getBearing,
    toRadians,
    toDegrees
};
