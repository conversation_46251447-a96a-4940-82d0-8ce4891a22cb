/* Global Styles */
:root {
    --primary-color: #4CAF50;
    --secondary-color: #2196F3;
    --background-color: #f5f5f5;
    --text-color: #333;
    --nav-height: 60px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
}

.navigation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--nav-height);
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    margin-left: 20px;
    font-weight: 500;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.main-content {
    margin-top: var(--nav-height);
    padding: 20px;
    min-height: calc(100vh - var(--nav-height));
    display: flex;
    justify-content: center;
    align-items: center;
}

#coordinate-input {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#location-btn {
    margin-top: 20px;
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

#location-btn:hover {
    background-color: #45a049;
}
