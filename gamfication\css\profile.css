.profile-container {
    margin-top: var(--nav-height);
    padding: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.avatar-section {
    text-align: center;
    margin-bottom: 40px;
}

#avatar-display {
    width: 200px;
    height: 200px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background-color: #ddd;
    overflow: hidden;
}

#customize-avatar {
    padding: 10px 20px;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.stats-section, .inventory-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

#player-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

#player-stats p {
    padding: 10px;
    background: #f8f8f8;
    border-radius: 4px;
}

#player-inventory {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 20px;
}

.inventory-item {
    aspect-ratio: 1;
    background: #f8f8f8;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s;
}

.inventory-item:hover {
    transform: scale(1.05);
}
