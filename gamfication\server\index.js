const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const cors = require('cors');
const { console } = require('inspector');
const fs = require('fs').promises;

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));
app.use('/css', express.static(path.join(__dirname, '../css')));
app.use('/javascript', express.static(path.join(__dirname, '../javascript')));

// In-memory storage (will be replaced with file storage)
const players = new Map();
const rooms = new Map();

// WebSocket connection handling
wss.on('connection', (ws) => {
    console.log('New client connected');

    ws.on('message', async (message) => {
        const data = JSON.parse(message);
        
        switch(data.type) {
            case 'JOIN':
                handlePlayerJoin(ws, data);
                break;
            case 'LOCATION_UPDATE':
                handleLocationUpdate(ws, data);
                break;
            case 'CHAT_MESSAGE':
                handleChatMessage(ws, data);
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    });

    ws.on('close', () => {
        if (ws.playerId) {
            players.delete(ws.playerId);
            broadcastPlayerList();
        }
    });
});

function handlePlayerJoin(ws, data) {
    const playerId = generateId();
    ws.playerId = playerId;
    
    players.set(playerId, {
        id: playerId,
        coordinates: data.coordinates,
        exp: 0,
        avatar: 'default-avatar.svg'
    });

    ws.send(JSON.stringify({
        type: 'JOINED',
        playerId: playerId,
        players: Array.from(players.values())
    }));

    broadcastPlayerList();
}

function handleLocationUpdate(ws, data) {
    const player = players.get(ws.playerId);
    if (player) {
        player.coordinates = data.coordinates;
        checkRoomEntry(player);
        broadcastPlayerList();
    }
}

function handleChatMessage(ws, data) {
    const player = players.get(ws.playerId);
    if (player && data.roomId) {
        broadcastToRoom(data.roomId, {
            type: 'CHAT_MESSAGE',
            playerId: ws.playerId,
            message: data.message,
            timestamp: new Date()
        });
    }
}

function broadcastPlayerList() {
    const playerList = Array.from(players.values());
    broadcast({
        type: 'PLAYER_LIST_UPDATE',
        players: playerList
    });
}

function broadcast(message) {
    wss.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(message));
        }
    });
}

function broadcastToRoom(roomId, message) {
    const room = rooms.get(roomId);
    if (room) {
        room.players.forEach(playerId => {
            const playerWs = Array.from(wss.clients).find(client => client.playerId === playerId);
            if (playerWs && playerWs.readyState === WebSocket.OPEN) {
                playerWs.send(JSON.stringify(message));
            }
        });
    }
}

function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

function checkRoomEntry(player) {
    // Implementation for checking if player has entered a room
    // Will be implemented with actual room coordinates
}

const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`http://localhost:${PORT}`)
});
