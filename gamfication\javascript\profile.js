let ws;
let playerData;

function connectWebSocket() {
    ws = new WebSocket('ws://localhost:3002');
    
    ws.onopen = () => {
        console.log('Connected to profile server');
        const playerId = localStorage.getItem('playerId');
        if (playerId) {
            requestPlayerData(playerId);
        } else {
            window.location.href = '/index.html';
        }
    };
    
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleServerMessage(data);
    };
    
    ws.onclose = () => {
        console.log('Disconnected from profile server');
        setTimeout(connectWebSocket, 3000);
    };
}

function requestPlayerData(playerId) {
    ws.send(JSON.stringify({
        type: 'GET_PLAYER_DATA',
        playerId: playerId
    }));
}

function handleServerMessage(data) {
    switch (data.type) {
        case 'PLAYER_DATA':
            updateProfile(data.player);
            break;
        case 'INVENTORY_UPDATE':
            updateInventory(data.inventory);
            break;
    }
}

function updateProfile(player) {
    playerData = player;
    
    // Update player stats
    document.getElementById('player-id').textContent = player.id;
    document.getElementById('exp-points').textContent = player.exp;
    document.getElementById('rooms-visited').textContent = player.roomsVisited || 0;
    document.getElementById('connections-made').textContent = player.connections || 0;

    // Update avatar
    const avatarDisplay = document.getElementById('avatar-display');
    avatarDisplay.style.backgroundImage = `url(${player.avatar})`;
}

function updateInventory(inventory) {
    const inventoryContainer = document.getElementById('player-inventory');
    inventoryContainer.innerHTML = '';

    inventory.forEach(item => {
        const itemElement = document.createElement('div');
        itemElement.className = 'inventory-item';
        itemElement.innerHTML = `
            <img src="${item.icon}" alt="${item.name}">
            <span class="item-name">${item.name}</span>
        `;
        
        // Add tooltip with item description
        itemElement.title = item.description;
        
        // Add click handler for using items
        itemElement.addEventListener('click', () => useItem(item));
        
        inventoryContainer.appendChild(itemElement);
    });
}

function useItem(item) {
    ws.send(JSON.stringify({
        type: 'USE_ITEM',
        itemId: item.id,
        playerId: playerData.id
    }));
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    connectWebSocket();

    // Set up avatar customization
    const customizeButton = document.getElementById('customize-avatar');
    if (customizeButton) {
        customizeButton.addEventListener('click', () => {
            // Implement avatar customization UI
            console.log('Avatar customization not implemented yet');
        });
    }
});
