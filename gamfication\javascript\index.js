// WebSocket connection
let ws;
let playerId = null;

// Connect to WebSocket server
function connectWebSocket() {
    ws = new WebSocket('ws://localhost:3002');
    
    ws.onopen = () => {
        console.log('Connected to server');
        if (playerId) {
            // Reconnection with existing ID
            ws.send(JSON.stringify({
                type: 'RECONNECT',
                playerId: playerId
            }));
        }
    };
    
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleServerMessage(data);
    };
    
    ws.onclose = () => {
        console.log('Disconnected from server');
        // Attempt to reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
    };
}

// Handle messages from server
function handleServerMessage(data) {
    switch (data.type) {
        case 'JOINED':
            playerId = data.playerId;
            localStorage.setItem('playerId', playerId);
            window.location.href = '/map.html';
            break;
        default:
            console.log('Unknown message type:', data.type);
    }
}

let watchId = null;

// Get user's location and join game
function joinGame() {
    if ('geolocation' in navigator) {
        // First get initial position
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const coordinates = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                // Join the game with initial position
                ws.send(JSON.stringify({
                    type: 'JOIN',
                    coordinates: coordinates
                }));

                // Start watching position
                watchId = navigator.geolocation.watchPosition(
                    (position) => {
                        const newCoordinates = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        
                        // Send location update
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'LOCATION_UPDATE',
                                coordinates: newCoordinates
                            }));
                        }
                    },
                    (error) => {
                        console.error('Error watching location:', error);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 5000,
                        maximumAge: 0
                    }
                );
            },
            (error) => {
                console.error('Error getting location:', error);
                alert('Please enable location services to play the game.');
            },
            {
                enableHighAccuracy: true,
                timeout: 5000,
                maximumAge: 0
            }
        );
    } else {
        alert('Geolocation is not supported by your browser');
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    // Connect to WebSocket server
    connectWebSocket();
    
    // Set up location button    const locationBtn = document.getElementById('location-btn');
    if (locationBtn) {
        locationBtn.addEventListener('click', () => {
            locationBtn.disabled = true;
            locationBtn.textContent = 'Connecting...';
            joinGame();
        });
    }
    
    // Check for existing player ID
    const existingPlayerId = localStorage.getItem('playerId');
    if (existingPlayerId) {
        playerId = existingPlayerId;
    }

    // Clean up when page is closed or changed
    window.addEventListener('beforeunload', () => {
        if (watchId !== null) {
            navigator.geolocation.clearWatch(watchId);
        }
        if (ws) {
            ws.close();
        }
    });
});
