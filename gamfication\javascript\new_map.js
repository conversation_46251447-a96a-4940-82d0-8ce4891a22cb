// Campus boundary coordinates for KLE College
const CAMPUS_BOUNDS = {
    coordinates: [
        [74.4743840505713, 15.79585950774381],  // Starting point
        [74.47376399333488, 15.79630331517275], // Next point
        [74.47380675590291, 15.796738304291807], // Next point
        [74.47422827264491, 15.79655020100362],  // Next point
        [74.474335179065, 15.796661887352048],   // Next point
        [74.47448484805308, 15.796573713924195], // Next point
        [74.47443597654676, 15.795918290240603], // Next point
        [74.4743840505713, 15.79585950774381]    // Back to start
    ]
};

// Define campus rooms/areas
const CAMPUS_ROOMS = [
    {
        id: 'main-building',
        name: 'Main Building',
        coordinates: [77.5861361, 12.9271833],
        radius: 20, // meters
        type: 'static'
    }
];

let map;
let ws;
let playerId;
let playerMarker = null;
let playerLocationCircle = null;
let watchId = null;
let markers = new Map(); // Store player markers
let currentRoom = null;

function initializeMap() {
    mapboxgl.accessToken = 'your_mapbox_token'; // Replace with your Mapbox token
      map = new mapboxgl.Map({
        container: 'map',
        style: 'mapbox://styles/mapbox/satellite-v9', // Using satellite view
        center: [74.47410941719395, 15.796299006017829], // Center of campus
        zoom: 19, // Higher zoom for better campus view
        minZoom: 17, // Restrict zoom out to keep focus on campus
        maxZoom: 21, // Allow closer zoom for detail
        pitch: 45, // Add a tilted view for better perspective
        bearing: -10 // Slight rotation for better orientation
    });

    map.on('load', () => {
        // Add campus boundary
        map.addLayer({
            id: 'campus-boundary',
            type: 'fill',
            source: {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    properties: {},
                    geometry: {
                        type: 'Polygon',
                        coordinates: [CAMPUS_BOUNDS.coordinates]
                    }
                }
            },
            paint: {
                'fill-color': '#4CAF50',
                'fill-opacity': 0.2,
                'fill-outline-color': '#4CAF50'
            }
        });

        // Add campus boundary line
        map.addLayer({
            id: 'campus-outline',
            type: 'line',
            source: {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    properties: {},
                    geometry: {
                        type: 'Polygon',
                        coordinates: [CAMPUS_BOUNDS.coordinates]
                    }
                }
            },
            paint: {
                'line-color': '#4CAF50',
                'line-width': 2
            }
        });

        // Add rooms
        CAMPUS_ROOMS.forEach(room => {
            // Add circle for each room
            map.addLayer({
                id: `room-${room.id}`,
                type: 'circle',
                source: {
                    type: 'geojson',
                    data: {
                        type: 'Feature',
                        properties: {
                            name: room.name
                        },
                        geometry: {
                            type: 'Point',
                            coordinates: room.coordinates
                        }
                    }
                },
                paint: {
                    'circle-radius': {
                        stops: [
                            [16, 5],
                            [20, 20]
                        ]
                    },
                    'circle-color': '#2196F3',
                    'circle-opacity': 0.5,
                    'circle-stroke-width': 2,
                    'circle-stroke-color': '#2196F3'
                }
            });

            // Add room labels
            map.addLayer({
                id: `room-label-${room.id}`,
                type: 'symbol',
                source: {
                    type: 'geojson',
                    data: {
                        type: 'Feature',
                        properties: {
                            name: room.name
                        },
                        geometry: {
                            type: 'Point',
                            coordinates: room.coordinates
                        }
                    }
                },
                layout: {
                    'text-field': ['get', 'name'],
                    'text-size': 12,
                    'text-offset': [0, -2],
                    'text-anchor': 'top'
                },
                paint: {
                    'text-color': '#ffffff',
                    'text-halo-color': '#000000',
                    'text-halo-width': 1
                }
            });
        });
    });

    // Add navigation controls
    map.addControl(new mapboxgl.NavigationControl(), 'top-right');
    
    // Add custom scale control
    map.addControl(new mapboxgl.ScaleControl({
        maxWidth: 100,
        unit: 'metric'
    }), 'bottom-left');

    // Restrict map bounds to campus area with padding    const bounds = new mapboxgl.LngLatBounds([
        [74.47368399333488, 15.79575950774381], // Southwest with padding
        [74.47458484805308, 15.796838304291807]  // Northeast with padding
    ]);
    map.setMaxBounds(bounds);

    // Connect to WebSocket
    connectWebSocket();

    // Start watching player location
    watchPlayerLocation();
}

// ... Rest of your existing WebSocket and location tracking code ...
