<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campus Map - Campus Quest</title>
    <link rel="stylesheet" href="/css/map.css">
    <link href='https://api.mapbox.com/mapbox-gl-js/v2.9.1/mapbox-gl.css' rel='stylesheet' />
</head>
<body>
    <div id="app">
        <nav class="navigation">
            <h1>Campus Map</h1>
            <div class="nav-links">
                <a href="/index.html">Home</a>
                <a href="/profile.html">Profile</a>
                <a href="/leaderboard.html">Leaderboard</a>
            </div>
        </nav>
        
        <div class="map-container">
            <div id="map"></div>
            <div id="player-info" class="player-overlay">
                <h3>Nearby Players</h3>
                <div id="nearby-players-list"></div>
            </div>
        </div>

        <div id="chat-overlay" class="hidden">
            <div class="chat-header">
                <h3>Room Chat</h3>
                <button id="close-chat">×</button>
            </div>
            <div id="chat-messages"></div>
            <div class="chat-input">
                <input type="text" id="message-input" placeholder="Type a message...">
                <button id="send-message">Send</button>
            </div>
        </div>
    </div>

    <script src='https://api.mapbox.com/mapbox-gl-js/v2.9.1/mapbox-gl.js'></script>
    <script src="/javascript/new_map.js"></script>
</body>
</html>
