.leaderboard-container {
    margin-top: var(--nav-height);
    padding: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.leaderboard-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.filter-btn {
    padding: 8px 16px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.leaderboard-list {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.leaderboard-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.leaderboard-item:last-child {
    border-bottom: none;
}

.rank {
    font-size: 24px;
    font-weight: bold;
    width: 50px;
}

.player-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
}

.player-info {
    flex-grow: 1;
}

.player-name {
    font-weight: 500;
}

.player-score {
    color: #666;
    font-size: 14px;
}

.top-3 {
    background-color: #f8f8f8;
}

.rank-1 {
    background-color: #fff7e6;
}

.rank-2 {
    background-color: #f5f5f5;
}

.rank-3 {
    background-color: #fff0e6;
}
