let map;
let ws;
let playerId;
let playerMarker = null;
let playerLocationCircle = null;
let watchId = null;
let markers = new Map();
let currentRoom = null;

// Campus boundary coordinates for KLE College
const CAMPUS_BOUNDS = {
    coordinates: [
        [74.4743840505713, 15.79585950774381],   // Starting point
        [74.47376399333488, 15.79630331517275],  // Next point
        [74.47380675590291, 15.796738304291807], // Next point
        [74.47422827264491, 15.79655020100362],  // Next point
        [74.474335179065, 15.796661887352048],   // Next point
        [74.47448484805308, 15.796573713924195], // Next point
        [74.47443597654676, 15.795918290240603], // Next point
        [74.4743840505713, 15.79585950774381]    // Back to start
    ]
};

// Define campus locations/rooms
const CAMPUS_LOCATIONS = [
    {
        id: 'main-entrance',
        name: 'Main Entrance',
        coordinates: [74.47410941719395, 15.796299006017829],
        radius: 15,
        type: 'entrance'
    },
    {
        id: 'main-building',
        name: 'Main Building',
        coordinates: [74.47422827264491, 15.79655020100362],
        radius: 25,
        type: 'academic'
    },
    {
        id: 'canteen',
        name: 'Canteen',
        coordinates: [74.47443597654676, 15.795918290240603],
        radius: 15,
        type: 'social'
    }
];

function initializeMap() {
    mapboxgl.accessToken = 'your_mapbox_token'; // Replace with your Mapbox token
    
    map = new mapboxgl.Map({
        container: 'map',
        style: 'mapbox://styles/mapbox/satellite-v9',
        center: [74.47410941719395, 15.796299006017829],
        zoom: 19,
        minZoom: 17,
        maxZoom: 21,
        pitch: 45,
        bearing: -10
    });

    // Add map controls
    map.addControl(new mapboxgl.NavigationControl());
    map.addControl(new mapboxgl.GeolocateControl({
        positionOptions: {
            enableHighAccuracy: true
        },
        trackUserLocation: true
    }));

    // Connect to WebSocket
    connectWebSocket();

    // Start watching player's location
    watchPlayerLocation();
}

function connectWebSocket() {
    ws = new WebSocket('ws://localhost:3002');
    
    ws.onopen = () => {
        console.log('Connected to map server');
        playerId = localStorage.getItem('playerId');
    };
    
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleServerMessage(data);
    };
    
    ws.onclose = () => {
        console.log('Disconnected from map server');
        setTimeout(connectWebSocket, 3000);
    };
}

function handleServerMessage(data) {
    switch (data.type) {
        case 'PLAYER_LIST_UPDATE':
            updatePlayerMarkers(data.players);
            break;
        case 'ROOM_ENTERED':
            handleRoomEntry(data.room);
            break;
        case 'CHAT_MESSAGE':
            displayChatMessage(data);
            break;
    }
}

function updatePlayerMarkers(players) {
    // Remove old markers
    markers.forEach((marker, id) => {
        if (!players.find(p => p.id === id)) {
            marker.remove();
            markers.delete(id);
        }
    });

    // Update or add new markers
    players.forEach(player => {
        if (player.id === playerId) return; // Skip self

        let marker = markers.get(player.id);
        if (!marker) {
            // Create new marker
            const el = document.createElement('div');
            el.className = 'player-marker';
            el.style.backgroundImage = `url(${player.avatar})`;
            
            marker = new mapboxgl.Marker(el)
                .setLngLat([player.coordinates.lng, player.coordinates.lat])
                .addTo(map);
            
            markers.set(player.id, marker);
        } else {
            // Update existing marker
            marker.setLngLat([player.coordinates.lng, player.coordinates.lat]);
        }
    });
}

let playerMarker = null;
let playerLocationCircle = null;
let watchId = null;

function watchPlayerLocation() {
    if ('geolocation' in navigator) {
        watchId = navigator.geolocation.watchPosition(
            (position) => {
                const coordinates = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                // Center map on first position
                if (!map.loaded) {
                    map.setCenter([coordinates.lng, coordinates.lat]);
                    map.setZoom(17);
                    map.loaded = true;

                    // Add player marker on first position
                    const el = document.createElement('div');
                    el.className = 'player-marker current-player';
                    playerMarker = new mapboxgl.Marker(el)
                        .setLngLat([coordinates.lng, coordinates.lat])
                        .addTo(map);

                    // Add accuracy circle
                    if (!playerLocationCircle) {
                        playerLocationCircle = new mapboxgl.Marker({
                            element: createAccuracyCircle(position.coords.accuracy),
                            anchor: 'center'
                        })
                        .setLngLat([coordinates.lng, coordinates.lat])
                        .addTo(map);
                    }
                } else {
                    // Update player marker and accuracy circle positions
                    playerMarker.setLngLat([coordinates.lng, coordinates.lat]);
                    if (playerLocationCircle) {
                        playerLocationCircle.setLngLat([coordinates.lng, coordinates.lat]);
                        updateAccuracyCircle(playerLocationCircle.getElement(), position.coords.accuracy);
                    }
                }

                // Send location update to server
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'LOCATION_UPDATE',
                        coordinates: coordinates,
                        accuracy: position.coords.accuracy
                    }));
                }
            },
            (error) => {
                console.error('Error watching location:', error);
                alert('Error getting location. Please ensure location services are enabled.');
            },
            {
                enableHighAccuracy: true,
                timeout: 5000,
                maximumAge: 0
            }
        );
    }
}

function createAccuracyCircle(accuracy) {
    const div = document.createElement('div');
    div.className = 'accuracy-circle';
    div.style.width = `${accuracy * 2}px`;
    div.style.height = `${accuracy * 2}px`;
    return div;
}

function updateAccuracyCircle(element, accuracy) {
    element.style.width = `${accuracy * 2}px`;
    element.style.height = `${accuracy * 2}px`;
}
}

function handleRoomEntry(room) {
    currentRoom = room;
    const chatOverlay = document.getElementById('chat-overlay');
    chatOverlay.classList.remove('hidden');
}

function displayChatMessage(data) {
    if (!currentRoom) return;
    
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';
    messageDiv.innerHTML = `
        <strong>${data.playerId === playerId ? 'You' : 'Player ' + data.playerId}</strong>:
        ${data.message}
    `;
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    initializeMap();

    // Set up chat functionality
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-message');
    const closeChatButton = document.getElementById('close-chat');

    sendButton.addEventListener('click', () => {
        if (!currentRoom || !messageInput.value.trim()) return;
        
        ws.send(JSON.stringify({
            type: 'CHAT_MESSAGE',
            roomId: currentRoom.id,
            message: messageInput.value.trim()
        }));
        
        messageInput.value = '';
    });

    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') sendButton.click();
    });

    closeChatButton.addEventListener('click', () => {
        document.getElementById('chat-overlay').classList.add('hidden');
        currentRoom = null;
    });
});
