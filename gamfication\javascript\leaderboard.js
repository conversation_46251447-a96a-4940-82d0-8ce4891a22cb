let ws;
let currentFilter = 'exp';

function connectWebSocket() {
    ws = new WebSocket('ws://localhost:3002');
    
    ws.onopen = () => {
        console.log('Connected to leaderboard server');
        requestLeaderboard();
    };
    
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'LEADERBOARD_DATA') {
            updateLeaderboard(data.players);
        }
    };
    
    ws.onclose = () => {
        console.log('Disconnected from leaderboard server');
        setTimeout(connectWebSocket, 3000);
    };
}

function requestLeaderboard() {
    ws.send(JSON.stringify({
        type: 'GET_LEADERBOARD',
        filter: currentFilter
    }));
}

function updateLeaderboard(players) {
    const leaderboardList = document.getElementById('leaderboard');
    leaderboardList.innerHTML = '';

    players.forEach((player, index) => {
        const rank = index + 1;
        const itemElement = document.createElement('div');
        itemElement.className = `leaderboard-item ${rank <= 3 ? `rank-${rank}` : ''}`;
        
        itemElement.innerHTML = `
            <div class="rank">#${rank}</div>
            <img class="player-avatar" src="${player.avatar}" alt="Player avatar">
            <div class="player-info">
                <div class="player-name">Player ${player.id}</div>
                <div class="player-score">
                    ${formatScore(player[currentFilter])} ${formatLabel(currentFilter)}
                </div>
            </div>
        `;
        
        leaderboardList.appendChild(itemElement);
    });
}

function formatScore(value) {
    return currentFilter === 'exp' ? value + ' XP' : value;
}

function formatLabel(filter) {
    switch (filter) {
        case 'exp':
            return 'Experience Points';
        case 'connections':
            return 'Connections Made';
        case 'rooms':
            return 'Rooms Discovered';
        default:
            return '';
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    connectWebSocket();

    // Set up filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Update active filter
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // Update leaderboard
            currentFilter = button.dataset.filter;
            requestLeaderboard();
        });
    });
});
