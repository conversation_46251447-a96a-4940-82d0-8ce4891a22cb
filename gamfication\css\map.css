.map-container {
    margin-top: var(--nav-height);
    height: calc(100vh - var(--nav-height));
    position: relative;
}

#map {
    width: 100%;
    height: 100%;
    background-color: #000;
}

/* Room highlight effect */
.room-highlight {
    background-color: rgba(33, 150, 243, 0.3);
    border: 2px solid #2196F3;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.4;
    }
    100% {
        transform: scale(1);
        opacity: 0.6;
    }
}

.player-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 300px;
    max-height: 400px;
    overflow-y: auto;
}

#chat-overlay {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: white;
    width: 300px;
    height: 400px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 10px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
}

.chat-input {
    padding: 10px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
}

#message-input {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

#send-message {
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.player-marker {
    width: 20px;
    height: 20px;
    background-color: #2196F3;
    border: 2px solid white;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.player-marker.current-player {
    background-color: #4CAF50;
    width: 24px;
    height: 24px;
    z-index: 2;
}

.accuracy-circle {
    background-color: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 50%;
    pointer-events: none;
    transform: translate(-50%, -50%);
}

.hidden {
    display: none !important;
}
