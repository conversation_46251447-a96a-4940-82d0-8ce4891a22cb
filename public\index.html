<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>College Arena - Gaming Platform</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/main.css">
    <link rel="font" href="https://www.fontspace.com/game-crack-font-f119802">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Frijole&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* General styles for the hero title */
        .hero-title {
            font-family: "Frijole", system-ui;
            font-size: 4.5rem;
            line-height: 1.2;
            text-align: center;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
        }

        /* Hero Gradient Text Styles */
        .hero-title .gradient-text {
            font-family: "Frijole", system-ui;
            font-size: 5.5rem;
            letter-spacing: 3px;
            text-shadow:
                0 0 10px rgba(59, 130, 246, 0.5),
                0 0 20px rgba(139, 92, 246, 0.3),
                0 0 30px rgba(236, 72, 153, 0.2);
            background: linear-gradient(90deg,
                #3b82f6,
                #8b5cf6,
                #ec4899,
                #8b5cf6,
                #3b82f6
            );
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: gradient 8s ease infinite;
            background-size: 300% 300%;
            display: inline-block;
            transform: perspective(500px) rotateX(5deg);
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            position: relative;
        }

        .hero-title .gradient-text::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                rgba(59, 130, 246, 0.1),
                rgba(139, 92, 246, 0.1),
                rgba(236, 72, 153, 0.1)
            );
            filter: blur(20px);
            z-index: -1;
            border-radius: 1rem;
            transform: scale(0.95);
            transition: transform 0.3s ease;
        }

        .hero-title .gradient-text:hover {
            transform: perspective(500px) rotateX(0deg) scale(1.05);
            letter-spacing: 4px;
        }

        .hero-title .gradient-text:hover::before {
            transform: scale(1.05);
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Add a loading state for the font */
        .font-loading {
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .font-loaded {
            opacity: 1;
        }

        /* Add a fallback style */
        @supports not (background-clip: text) {
            .hero-title .gradient-text {
                background: none;
                color: #3b82f6;
                text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
            }
        }

        /* CSS for Rotating Text Animation */
        .text-entry {
            animation: slideInUp var(--animation-duration) ease-out forwards;
        }

        .text-exit {
            animation: slideOutDown var(--animation-duration) ease-in forwards;
        }

        /* Define animation duration variable */
        :root {
            --animation-duration: 0.5s;
        }

        @keyframes slideInUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideOutDown {
            from {
                transform: translateY(0);
                opacity: 1;
            }
            to {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        /* Button container styles */
        #guestButtons, #userButtons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        @media (min-width: 640px) {
            #guestButtons, #userButtons {
                flex-direction: row;
            }
        }
    </style>
</head>
<body class="bg-dark-900 text-gray-100">
    <!-- Navigation -->
    <nav class="navbar fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="flex-shrink-0 flex items-center">
                        <span class="text-2xl font-gaming font-bold gradient-text tracking-wider">College Arena</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#games" class="nav-link">Games</a>
                    <a href="#tournaments" class="nav-link">Tournaments</a>
                    <a href="#leaderboard" class="nav-link">Leaderboard</a>
                    <a href="#contact" class="nav-link">Contact</a>
                    <!-- Profile Menu -->
                    <div id="profileMenu" class="hidden relative group">
                        <button class="profile-menu-button flex items-center space-x-2 text-gray-300 hover:text-white transition-colors duration-200">
                            <img id="navAvatar" src="/images/default-avatar.png" alt="Profile" class="w-8 h-8 rounded-full border-2 border-primary-500">
                            <span id="navUserName" class="text-sm font-medium">Loading...</span>
                            <i class="fas fa-chevron-down text-xs transition-transform duration-200 group-hover:rotate-180"></i>
                        </button>
                        <!-- Profile Dropdown -->
                        <div class="profile-dropdown absolute right-0 mt-2 w-48 rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <div class="p-4 border-b border-dark-700">
                                <div class="text-sm font-medium text-white" id="dropdownUserName">Loading...</div>
                                <div class="text-xs text-gray-400" id="dropdownUserEmail">Loading...</div>
                            </div>
                            <div class="py-2">
                                <a href="/profil.html" class="block px-4 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-primary-400 transition-colors duration-200">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <a href="#settings" class="block px-4 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-primary-400 transition-colors duration-200">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                                <button id="navLogoutBtn" class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors duration-200">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Auth Buttons -->
                    <div id="authButtons" class="flex items-center space-x-4">
                        <a href="/login" class="btn-outline">Login</a>
                        <a href="/register.html" class="btn-primary">Join Now</a>
                    </div>
                </div>
                <!-- Mobile Menu Button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-300 hover:text-primary-400 transition-colors duration-200">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="mobile-menu hidden md:hidden">
            <div class="px-4 pt-2 pb-3 space-y-3">
                <a href="#games" class="block nav-link">Games</a>
                <a href="#tournaments" class="block nav-link">Tournaments</a>
                <a href="#leaderboard" class="block nav-link">Leaderboard</a>
                <a href="#contact" class="block nav-link">Contact</a>
                <div class="mobile-auth-buttons">
                    <!-- Mobile auth buttons will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient">
        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>

        <div class="hero-content">
            <h1 class="hero-title">
                <span class="block" style="font-family: 'Orbitron', sans-serif;">Level Up Your</span>
                <span id="animatingText" class="gradient-text">College Experience</span>
            </h1>
            <p class="hero-description">
                Join the ultimate gaming platform for college students. Compete, collaborate, and conquer in epic tournaments and challenges that will transform your academic journey.
            </p>
            <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <!-- Buttons for non-logged in users -->
                <div id="guestButtons" class="flex flex-col sm:flex-row gap-4">
                    <a href="/register.html" class="btn-primary glow">
                        Start Gaming
                        <i class="fas fa-gamepad ml-2"></i>
                    </a>
                    <button class="btn-outline">
                        Watch Trailer
                        <i class="fas fa-play ml-2"></i>
                    </button>
                </div>

                <!-- Start button for logged in users -->
                <div id="userButtons" class="hidden flex flex-col sm:flex-row gap-4">
                    <button id="startGameBtn" class="btn-primary glow">
                        Start
                        <i class="fas fa-rocket ml-2"></i>
                    </button>
                    <button class="btn-outline">
                        Watch Trailer
                        <i class="fas fa-play ml-2"></i>
                    </button>
                </div>
            </div>

            <!-- Hero Stats -->
            <div class="hero-stats">
                <div class="hero-stat">
                    <div class="hero-stat-number">10K+</div>
                    <div class="hero-stat-label">Active Players</div>
                </div>
                <div class="hero-stat">
                    <div class="hero-stat-number">500+</div>
                    <div class="hero-stat-label">Daily Matches</div>
                </div>
                <div class="hero-stat">
                    <div class="hero-stat-number">50+</div>
                    <div class="hero-stat-label">Colleges</div>
                </div>
                <div class="hero-stat">
                    <div class="hero-stat-number">$100K+</div>
                    <div class="hero-stat-label">Prize Pool</div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-primary-500 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-primary-500 rounded-full mt-2 animate-scroll"></div>
            </div>
        </div>
    </section>

    <!-- Profile Stats Section (shown when logged in) -->
    <section id="profileStats" class="hidden py-16 bg-gradient-to-b from-dark-800/50 to-dark-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-dark-800 rounded-2xl border border-dark-700 p-8">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-4">
                        <img id="profileStatsAvatar" src="/images/default-avatar.png" alt="Profile"
                             class="w-16 h-16 rounded-full border-2 border-primary-500">
                        <div>
                            <h2 id="profileStatsName" class="text-2xl font-gaming font-bold text-white">Loading...</h2>
                            <p id="profileStatsRank" class="text-primary-400 text-sm">Loading...</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-400" id="profileStatsLevel">Level 1</div>
                            <div class="text-sm text-gray-400">Current Level</div>
                        </div>
                        <div class="h-12 w-px bg-dark-600"></div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-400" id="profileStatsPoints">0</div>
                            <div class="text-sm text-gray-400">Total Points</div>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="stats-card">
                        <div class="stats-number" id="profileGamesPlayed">0</div>
                        <div class="stats-label">Games Played</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="profileTournamentsWon">0</div>
                        <div class="stats-label">Tournaments Won</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="profileAchievements">0</div>
                        <div class="stats-label">Achievements</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="profileWinRate">0%</div>
                        <div class="stats-label">Win Rate</div>
                    </div>
                </div>
                <!-- Recent Activity -->
                <div class="mt-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Recent Activity</h3>
                    <div class="space-y-4" id="recentActivity">
                        <!-- Activity items will be dynamically added here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-dark-800/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="stats-card text-center">
                    <div class="stats-number">10K+</div>
                    <div class="stats-label">Active Players</div>
                </div>
                <div class="stats-card text-center">
                    <div class="stats-number">500+</div>
                    <div class="stats-label">Tournaments</div>
                </div>
                <div class="stats-card text-center">
                    <div class="stats-number">50+</div>
                    <div class="stats-label">Colleges</div>
                </div>
                <div class="stats-card text-center">
                    <div class="stats-number">$100K+</div>
                    <div class="stats-label">Prize Pool</div>
                </div>
            </div>
        </div>
    </section>

     <!-- Featured Games -->
    <section id="games" class="games-section">
        <div class="games-header">
            <h2 class="section-title">
                Featured <span class="gradient-text">Games</span>
            </h2>
            <p class="section-subtitle">
                Choose from our selection of competitive games and start your journey to the top
            </p>
        </div>
        <div class="feature-grid">
            <!-- Game 1 -->
            <div class="game-card group">
                <span class="game-card-badge">Most Popular</span>
                <div class="game-card-icon group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chess"></i>
                </div>
                <h3 class="game-card-title">Strategic Chess</h3>
                <p class="game-card-description">
                    Master the art of strategy in our competitive chess tournaments. Perfect your skills and climb the ranks.
                </p>
                <div class="game-card-stats">
                    <div class="game-card-stat">
                        <i class="fas fa-users"></i>
                        <span>2.5K Players</span>
                    </div>
                    <div class="game-card-stat">
                        <i class="fas fa-trophy"></i>
                        <span>Daily Rewards</span>
                    </div>
                </div>
            </div>
            <!-- Game 2 -->
            <div class="game-card group">
                <span class="game-card-badge">New</span>
                <div class="game-card-icon group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-code"></i>
                </div>
                <h3 class="game-card-title">Code Wars</h3>
                <p class="game-card-description">
                    Battle it out in coding challenges. Solve problems, optimize solutions, and prove your programming prowess.
                </p>
                <div class="game-card-stats">
                    <div class="game-card-stat">
                        <i class="fas fa-users"></i>
                        <span>1.8K Players</span>
                    </div>
                    <div class="game-card-stat">
                        <i class="fas fa-trophy"></i>
                        <span>Weekly Prizes</span>
                    </div>
                </div>
            </div>
            <!-- Game 3 -->
             <div class="game-card group">
                <span class="game-card-badge">Trending</span>
                <div class="game-card-icon group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 class="game-card-title">Quiz Masters</h3>
                <p class="game-card-description">
                    Test your knowledge in various subjects. Compete in real-time quizzes and earn recognition.
                </p>
                <div class="game-card-stats">
                    <div class="game-card-stat">
                        <i class="fas fa-users"></i>
                        <span>3.2K Players</span>
                    </div>
                    <div class="game-card-stat">
                        <i class="fas fa-trophy"></i>
                        <span>Instant Rewards</span>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Contact Section -->
    <section id="contact" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title">
                    Get in <span class="gradient-text">Touch</span>
                </h2>
                <p class="section-subtitle">
                    Have questions? We'd love to hear from you.
                </p>
            </div>
            <div class="max-w-lg mx-auto">
                <form class="form-group">
                    <div>
                        <label for="name" class="form-label">Name</label>
                        <input type="text" name="name" id="name" class="form-input" placeholder="Your name">
                    </div>
                    <div>
                        <label for="email" class="form-label">Email</label>
                        <input type="email" name="email" id="email" class="form-input" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label for="message" class="form-label">Message</label>
                        <textarea id="message" name="message" rows="4" class="form-textarea" placeholder="Your message"></textarea>
                    </div>
                    <div>
                        <button type="submit" class="btn-primary w-full">
                            Send Message
                            <i class="fas fa-paper-plane ml-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark-800 border-t border-dark-700">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <h3 class="text-xl font-gaming font-bold gradient-text mb-4">College Arena</h3>
                    <p class="text-gray-400">
                        The ultimate gaming platform for college students. Join us in creating an exciting and competitive gaming community.
                    </p>
                </div>
                <div>
                    <h3 class="text-white text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#games" class="footer-link">Games</a></li>
                        <li><a href="#tournaments" class="footer-link">Tournaments</a></li>
                        <li><a href="#leaderboard" class="footer-link">Leaderboard</a></li>
                        <li><a href="#contact" class="footer-link">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-white text-lg font-semibold mb-4">Connect With Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="social-icon">
                            <i class="fab fa-discord"></i>
                        </a>
                        <a href="#" class="social-icon">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-icon">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="mt-8 border-t border-dark-700 pt-8">
                <p class="text-center text-gray-400">
                    © 2024 College Arena. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content login-modal">
            <div class="modal-header">
                <h2 class="modal-title">Welcome Back!</h2>
                <button class="modal-close" id="closeLoginModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label class="form-label" for="loginEmail">Email</label>
                        <div class="input-group">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="loginEmail" name="email" class="input-field"
                                   placeholder="<EMAIL>" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="loginPassword">Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="loginPassword" name="password" class="input-field"
                                   placeholder="Enter your password" required>
                            <button type="button" class="password-toggle" id="loginPasswordToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group flex items-center justify-between">
                        <label class="remember-me">
                            <input type="checkbox" id="loginRemember" name="remember" class="form-checkbox">
                            <span class="ml-2 text-sm text-gray-400">Remember me</span>
                        </label>
                        <a href="#" class="text-sm text-primary-400 hover:text-primary-300">Forgot password?</a>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn-primary w-full">
                            Sign In
                            <i class="fas fa-sign-in-alt ml-2"></i>
                        </button>
                    </div>

                    <div class="login-divider">
                        <span class="login-divider-text">or continue with</span>
                    </div>

                    <div class="social-login">
                        <button type="button" class="social-btn google">
                            <i class="fab fa-google"></i>
                            Google
                        </button>
                        <button type="button" class="social-btn github">
                            <i class="fab fa-github"></i>
                            GitHub
                        </button>
                    </div>
                </form>
                <div class="login-footer text-center mt-6">
                    <p class="text-gray-400">
                        Don't have an account?
                        <a href="/register.html" class="text-primary-400 hover:text-primary-300">Sign up</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loginLoadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Error Modal -->
    <div id="loginErrorModal" class="modal">
        <div class="modal-content error-modal">
            <div class="modal-header">
                <h3 class="modal-title text-red-400">Error</h3>
                <button class="modal-close" id="closeLoginErrorModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="loginErrorMessage" class="text-gray-300"></p>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" id="loginErrorModalClose">Close</button>
            </div>
        </div>
    </div>

    <script src="/js/index.js"></script>
    <script>
        // Global functions and variables
        const DEFAULT_AVATAR = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PHBhdGggZD0iTTIwIDIxdi0yYTQgNCAwIDAgMC00LTRINGE0IDQgMCAwIDAtNCA0djIiPjwvcGF0aD48Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiPjwvY2lyY2xlPjwvc3ZnPg==';

        const getActivityIcon = (type) => {
            const icons = {
                'game_played': 'fa-gamepad',
                'tournament_won': 'fa-trophy',
                'achievement': 'fa-medal',
                'level_up': 'fa-star',
                'login': 'fa-sign-in-alt',
                'default': 'fa-circle'
            };
            return icons[type] || icons.default;
        };

        const formatDate = (dateString) => {
            const date = new Date(dateString);
            return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
                Math.ceil((date - new Date()) / (1000 * 60 * 60 * 24)),
                'day'
            );
        };

        // Add activity to user profile
        function addActivity(user, type, title) {
            if (!user.profile) {
                user.profile = {
                    avatar: DEFAULT_AVATAR,
                    stats: {
                        level: 1,
                        rank: 'Bronze',
                        totalPoints: 0,
                        gamesPlayed: 0,
                        tournamentsWon: 0
                    },
                    achievements: [],
                    activities: []
                };
            }

            if (!user.profile.activities) {
                user.profile.activities = [];
            }

            const activity = {
                type,
                title,
                timestamp: new Date().toISOString()
            };

            user.profile.activities.unshift(activity); // Add to beginning of array
            // Keep only last 10 activities
            user.profile.activities = user.profile.activities.slice(0, 10);

            // Update session storage
            sessionStorage.setItem('user', JSON.stringify(user));

            return activity;
        }

        // Update the updateProfileUI function
        function updateProfileUI(user) {
            if (!user) {
                console.error('No user data provided to updateProfileUI');
                return;
            }

            // Ensure user has a profile object with activities
            if (!user.profile) {
                user.profile = {
                    avatar: DEFAULT_AVATAR,
                    stats: {
                        level: 1,
                        rank: 'Bronze',
                        totalPoints: 0,
                        gamesPlayed: 0,
                        tournamentsWon: 0
                    },
                    achievements: [],
                    activities: []
                };
            }

            if (!user.profile.activities) {
                user.profile.activities = [];
            }

            // Get DOM elements
            const profileMenu = document.getElementById('profileMenu');
            const authButtons = document.getElementById('authButtons');
            const profileStats = document.getElementById('profileStats');
            const guestButtons = document.getElementById('guestButtons');
            const userButtons = document.getElementById('userButtons');

            // Show profile menu, hide auth buttons, show user buttons
            if (profileMenu) profileMenu.classList.remove('hidden');
            if (authButtons) authButtons.classList.add('hidden');
            if (profileStats) profileStats.classList.remove('hidden');
            if (guestButtons) guestButtons.classList.add('hidden');
            if (userButtons) userButtons.classList.remove('hidden');

            // Update navigation profile
            const navAvatar = document.getElementById('navAvatar');
            const navUserName = document.getElementById('navUserName');
            const dropdownUserName = document.getElementById('dropdownUserName');
            const dropdownUserEmail = document.getElementById('dropdownUserEmail');

            if (navAvatar) navAvatar.src = user.profile.avatar || DEFAULT_AVATAR;
            if (navUserName) navUserName.textContent = user.name;
            if (dropdownUserName) dropdownUserName.textContent = user.name;
            if (dropdownUserEmail) dropdownUserEmail.textContent = user.email;

            // Update profile stats
            const statsElements = {
                avatar: document.getElementById('profileStatsAvatar'),
                name: document.getElementById('profileStatsName'),
                rank: document.getElementById('profileStatsRank'),
                level: document.getElementById('profileStatsLevel'),
                points: document.getElementById('profileStatsPoints'),
                gamesPlayed: document.getElementById('profileGamesPlayed'),
                tournamentsWon: document.getElementById('profileTournamentsWon'),
                achievements: document.getElementById('profileAchievements'),
                winRate: document.getElementById('profileWinRate')
            };

            // Update all stats elements
            if (statsElements.avatar) statsElements.avatar.src = user.profile.avatar || DEFAULT_AVATAR;
            if (statsElements.name) statsElements.name.textContent = user.name;
            if (statsElements.rank) statsElements.rank.textContent = user.profile.stats.rank;
            if (statsElements.level) statsElements.level.textContent = `Level ${user.profile.stats.level}`;
            if (statsElements.points) statsElements.points.textContent = user.profile.stats.totalPoints;
            if (statsElements.gamesPlayed) statsElements.gamesPlayed.textContent = user.profile.stats.gamesPlayed;
            if (statsElements.tournamentsWon) statsElements.tournamentsWon.textContent = user.profile.stats.tournamentsWon;
            if (statsElements.achievements) {
                const unlockedAchievements = user.profile.achievements.filter(a => a.unlocked).length;
                statsElements.achievements.textContent = unlockedAchievements;
            }

            // Calculate and update win rate
            if (statsElements.winRate) {
                const winRate = user.profile.stats.gamesPlayed > 0
                    ? Math.round((user.profile.stats.tournamentsWon / user.profile.stats.gamesPlayed) * 100)
                    : 0;
                statsElements.winRate.textContent = `${winRate}%`;
            }

            // Update recent activity with improved styling
            const recentActivity = document.getElementById('recentActivity');
            if (recentActivity && user.profile.activities) {
                recentActivity.innerHTML = user.profile.activities
                    .slice(0, 5) // Show last 5 activities
                    .map(activity => {
                        const iconClass = getActivityIcon(activity.type);
                        const activityClass = getActivityClass(activity.type);
                        return `
                            <div class="flex items-center space-x-4 p-4 bg-dark-700/50 rounded-lg hover:bg-dark-700/70 transition-colors duration-200">
                                <div class="w-10 h-10 rounded-full ${activityClass} flex items-center justify-center text-primary-400">
                                    <i class="fas ${iconClass}"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="text-white font-medium">${activity.title}</div>
                                    <div class="text-sm text-gray-400">${formatDate(activity.timestamp)}</div>
                                </div>
                            </div>
                        `;
                    }).join('') || `
                        <div class="text-center text-gray-400 py-4">
                            No recent activity
                        </div>
                    `;
            }

            // Update mobile menu
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                const mobileAuthButtons = mobileMenu.querySelector('.mobile-auth-buttons');
                if (mobileAuthButtons) {
                    mobileAuthButtons.innerHTML = `
                        <div class="flex items-center space-x-3 p-4 border-b border-dark-700">
                            <img src="${user.profile.avatar || DEFAULT_AVATAR}"
                                 alt="${user.name}"
                                 class="w-10 h-10 rounded-full border-2 border-primary-500">
                            <div>
                                <div class="text-white font-medium">${user.name}</div>
                                <div class="text-sm text-gray-400">${user.profile.stats.rank}</div>
                            </div>
                        </div>
                        <a href="/profil.html" class="block px-4 py-2 text-gray-300 hover:bg-dark-700 hover:text-primary-400">
                            <i class="fas fa-user mr-2"></i>Profile
                        </a>
                        <a href="#settings" class="block px-4 py-2 text-gray-300 hover:bg-dark-700 hover:text-primary-400">
                            <i class="fas fa-cog mr-2"></i>Settings
                        </a>
                        <button id="mobileLogoutBtn" class="w-full text-left px-4 py-2 text-gray-300 hover:bg-dark-700 hover:text-red-400">
                            <i class="fas fa-sign-out-alt mr-2"></i>Logout
                        </button>
                    `;

                    // Add mobile logout handler
                    const mobileLogoutBtn = document.getElementById('mobileLogoutBtn');
                    if (mobileLogoutBtn) {
                        mobileLogoutBtn.addEventListener('click', handleLogout);
                    }
                }
            }

            // Add a welcome notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-primary-500 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <i class="fas fa-check-circle"></i>
                    <span>Welcome back, ${user.name}!</span>
                </div>
            `;
            document.body.appendChild(notification);

            // Animate notification
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(full)';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Handle logged out state
        function handleLoggedOutState() {
            const profileMenu = document.getElementById('profileMenu');
            const authButtons = document.getElementById('authButtons');
            const profileStats = document.getElementById('profileStats');
            const mobileMenu = document.getElementById('mobile-menu');
            const guestButtons = document.getElementById('guestButtons');
            const userButtons = document.getElementById('userButtons');

            if (profileMenu) profileMenu.classList.add('hidden');
            if (authButtons) authButtons.classList.remove('hidden');
            if (profileStats) profileStats.classList.add('hidden');
            if (guestButtons) guestButtons.classList.remove('hidden');
            if (userButtons) userButtons.classList.add('hidden');

            sessionStorage.removeItem('user');
            sessionStorage.removeItem('token');

            // Update mobile menu
            if (mobileMenu) {
                const mobileAuthButtons = mobileMenu.querySelector('.mobile-auth-buttons');
                if (mobileAuthButtons) {
                    mobileAuthButtons.innerHTML = `
                        <a href="/login" class="btn-outline w-full mb-2">Login</a>
                        <a href="/register.html" class="btn-primary w-full">Join Now</a>
                    `;
                }
            }
        }

        // Update the handleLogout function
        async function handleLogout(event) {
            if (event) {
                event.preventDefault();
            }

            try {
                // Get current user data before logout
                const storedUser = sessionStorage.getItem('user');
                const user = storedUser ? JSON.parse(storedUser) : null;

                // Show loading state
                const logoutButtons = document.querySelectorAll('#navLogoutBtn, #mobileLogoutBtn');
                logoutButtons.forEach(btn => {
                    if (btn) {
                        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging out...';
                        btn.disabled = true;
                    }
                });

                // Call logout endpoint
                const response = await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    credentials: 'include'
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || 'Logout failed');
                }

                // Add logout activity before clearing session
                if (user) {
                    addActivity(user, 'logout', 'Logged out successfully');
                }

                // Clear all session data
                sessionStorage.removeItem('user');
                sessionStorage.removeItem('token');

                // Update UI to logged out state
                handleLoggedOutState();

                // Show success notification
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-dark-700 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 z-50';
                notification.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-check-circle text-green-400"></i>
                        <span>${result.message || 'Successfully logged out'}</span>
                    </div>
                `;
                document.body.appendChild(notification);

                // Animate notification
                requestAnimationFrame(() => {
                    notification.style.transform = 'translateX(0)';
                });

                // Remove notification and redirect
                setTimeout(() => {
                    notification.style.transform = 'translateX(full)';
                    setTimeout(() => {
                        notification.remove();
                        // Use replace instead of href to prevent back button from returning to logged-in state
                        window.location.replace('/');
                    }, 300);
                }, 2000);

            } catch (error) {
                console.error('Logout error:', error);

                // Reset button states
                const logoutButtons = document.querySelectorAll('#navLogoutBtn, #mobileLogoutBtn');
                logoutButtons.forEach(btn => {
                    if (btn) {
                        btn.innerHTML = '<i class="fas fa-sign-out-alt"></i> Logout';
                        btn.disabled = false;
                    }
                });

                // Show error notification with specific message
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
                notification.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>${error.message || 'Error during logout. Please try again.'}</span>
                    </div>
                `;
                document.body.appendChild(notification);
                setTimeout(() => notification.remove(), 3000);
            }
        }

        // Add activity class helper
        function getActivityClass(type) {
            const classes = {
                'login': 'bg-green-500/20',
                'logout': 'bg-red-500/20',
                'game_played': 'bg-blue-500/20',
                'tournament_won': 'bg-yellow-500/20',
                'achievement': 'bg-purple-500/20',
                'level_up': 'bg-pink-500/20',
                'default': 'bg-primary-500/20'
            };
            return classes[type] || classes.default;
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Start button functionality
            const startGameBtn = document.getElementById('startGameBtn');
            if (startGameBtn) {
                startGameBtn.addEventListener('click', () => {
                    // Navigate to the map page
                    window.location.href = '/gamfication/public/map.html';
                });
            }

            // Profile functionality
            const checkAuth = async () => {
                try {
                    // First check session storage
                    const storedUser = sessionStorage.getItem('user');
                    if (storedUser) {
                        try {
                            const user = JSON.parse(storedUser);
                            updateProfileUI(user);
                            return user;
                        } catch (e) {
                            sessionStorage.removeItem('user');
                        }
                    }

                    // If no stored user, check with server
                    const response = await fetch('/api/auth/check', {
                        credentials: 'include'
                    });

                    if (!response.ok) {
                        handleLoggedOutState();
                        return null;
                    }

                    const user = await response.json();
                    // Store user in session storage
                    sessionStorage.setItem('user', JSON.stringify(user));
                    updateProfileUI(user);
                    return user;
                } catch (error) {
                    console.error('Auth check failed:', error);
                    handleLoggedOutState();
                    return null;
                }
            };

            // Login Modal Functionality
            const loginModal = document.getElementById('loginModal');
            const loginForm = document.getElementById('loginForm');
            const closeLoginModal = document.getElementById('closeLoginModal');
            const loginPasswordToggle = document.getElementById('loginPasswordToggle');
            const loginLoadingOverlay = document.getElementById('loginLoadingOverlay');
            const loginErrorModal = document.getElementById('loginErrorModal');
            const loginErrorMessage = document.getElementById('loginErrorMessage');
            const closeLoginErrorModal = document.getElementById('closeLoginErrorModal');
            const loginErrorModalClose = document.getElementById('loginErrorModalClose');

            // Show login modal
            const showLoginModal = () => {
                loginModal.classList.add('active');
                document.body.style.overflow = 'hidden';
            };

            // Hide login modal
            const hideLoginModal = () => {
                loginModal.classList.remove('active');
                document.body.style.overflow = '';
                loginForm.reset();
            };

            // Show error modal
            const showLoginError = (message) => {
                loginErrorMessage.textContent = message;
                loginErrorModal.classList.add('active');
            };

            // Hide error modal
            const hideLoginError = () => {
                loginErrorModal.classList.remove('active');
            };

            // Update login buttons to show modal
            document.querySelectorAll('a[href="/login"]').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    showLoginModal();
                });
            });

            // Close modal handlers
            closeLoginModal.addEventListener('click', hideLoginModal);
            loginErrorModalClose.addEventListener('click', hideLoginError);
            closeLoginErrorModal.addEventListener('click', hideLoginError);

            // Close modal when clicking outside
            loginModal.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    hideLoginModal();
                }
            });

            loginErrorModal.addEventListener('click', (e) => {
                if (e.target === loginErrorModal) {
                    hideLoginError();
                }
            });

            // Password toggle
            loginPasswordToggle.addEventListener('click', () => {
                const passwordInput = document.getElementById('loginPassword');
                const type = passwordInput.type === 'password' ? 'text' : 'password';
                passwordInput.type = type;
                loginPasswordToggle.innerHTML = `<i class="fas fa-${type === 'password' ? 'eye' : 'eye-slash'}"></i>`;
            });

            // Handle form submission
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                const formData = new FormData(loginForm);
                const data = {
                    email: formData.get('email'),
                    password: formData.get('password'),
                    remember: formData.get('remember') === 'on'
                };

                try {
                    loginLoadingOverlay.classList.add('active');

                    const response = await fetch('/api/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data),
                        credentials: 'include'
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Login failed');
                    }

                    // Add login activity
                    addActivity(result.user, 'login', 'Logged in successfully');

                    // Store user info and token in session storage
                    sessionStorage.setItem('user', JSON.stringify(result.user));
                    if (result.token) {
                        sessionStorage.setItem('token', result.token);
                    }

                    // Hide login modal
                    hideLoginModal();

                    // Update UI with user data
                    updateProfileUI(result.user);

                    // Show profile section with smooth scroll
                    const profileStats = document.getElementById('profileStats');
                    if (profileStats) {
                        profileStats.scrollIntoView({ behavior: 'smooth' });
                    }

                } catch (error) {
                    console.error('Login error:', error);
                    showLoginError(error.message || 'An error occurred during login. Please try again.');
                } finally {
                    loginLoadingOverlay.classList.remove('active');
                }
            });

            // Social login handlers
            document.querySelector('.social-btn.google').addEventListener('click', () => {
                // Implement Google OAuth login
                console.log('Google login clicked');
            });

            document.querySelector('.social-btn.github').addEventListener('click', () => {
                // Implement GitHub OAuth login
                console.log('GitHub login clicked');
            });

            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', () => {
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (mobileMenu) {
                        mobileMenu.classList.toggle('hidden');
                    }
                });
            }

            // Initialize auth check
            checkAuth();

            // Attach logout handlers to both desktop and mobile logout buttons
            const navLogoutBtn = document.getElementById('navLogoutBtn');
            const mobileLogoutBtn = document.getElementById('mobileLogoutBtn');

            if (navLogoutBtn) {
                navLogoutBtn.addEventListener('click', handleLogout);
            }

            if (mobileLogoutBtn) {
                mobileLogoutBtn.addEventListener('click', handleLogout);
            }

            // Update mobile menu to include logout button
            const updateMobileMenu = (user) => {
                const mobileMenu = document.getElementById('mobile-menu');
                if (mobileMenu) {
                    const mobileAuthButtons = mobileMenu.querySelector('.mobile-auth-buttons');
                    if (mobileAuthButtons) {
                        if (user) {
                            // User is logged in
                            mobileAuthButtons.innerHTML = `
                                <div class="flex items-center space-x-3 p-4 border-b border-dark-700">
                                    <img src="${user.profile.avatar || DEFAULT_AVATAR}"
                                         alt="${user.name}"
                                         class="w-10 h-10 rounded-full border-2 border-primary-500">
                                    <div>
                                        <div class="text-white font-medium">${user.name}</div>
                                        <div class="text-sm text-gray-400">${user.profile.stats.rank}</div>
                                    </div>
                                </div>
                                <a href="/profil.html" class="block px-4 py-2 text-gray-300 hover:bg-dark-700 hover:text-primary-400">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <a href="#settings" class="block px-4 py-2 text-gray-300 hover:bg-dark-700 hover:text-primary-400">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                                <button id="mobileLogoutBtn" class="w-full text-left px-4 py-2 text-gray-300 hover:bg-dark-700 hover:text-red-400">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </button>
                            `;
                            // Reattach logout handler to the new mobile logout button
                            const newMobileLogoutBtn = document.getElementById('mobileLogoutBtn');
                            if (newMobileLogoutBtn) {
                                newMobileLogoutBtn.addEventListener('click', handleLogout);
                            }
                        } else {
                            // User is logged out
                            mobileAuthButtons.innerHTML = `
                                <a href="/login" class="btn-outline w-full mb-2">Login</a>
                                <a href="/register.html" class="btn-primary w-full">Join Now</a>
                            `;
                        }
                    }
                }
            };

            // Update handleLoggedOutState to use the new updateMobileMenu function
            const handleLoggedOutState = () => {
                const profileMenu = document.getElementById('profileMenu');
                const authButtons = document.getElementById('authButtons');
                const profileStats = document.getElementById('profileStats');

                if (profileMenu) profileMenu.classList.add('hidden');
                if (authButtons) authButtons.classList.remove('hidden');
                if (profileStats) profileStats.classList.add('hidden');

                sessionStorage.removeItem('user');
                sessionStorage.removeItem('token');

                // Update mobile menu for logged out state
                updateMobileMenu(null);
            };

            // Update updateProfileUI to use the new updateMobileMenu function
            const originalUpdateProfileUI = updateProfileUI;
            updateProfileUI = (user) => {
                originalUpdateProfileUI(user);
                updateMobileMenu(user);
            };

            // Update the notification creation function
            function createNotification(message, type = 'success') {
                const notification = document.createElement('div');
                notification.className = 'notification';

                const bgColor = type === 'success' ? 'bg-green-500/20' : 'bg-red-500/20';
                const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
                const iconColor = type === 'success' ? 'text-green-400' : 'text-red-400';

                notification.innerHTML = `
                    <div class="notification-content">
                        <div class="flex items-center space-x-2">
                            <i class="fas ${icon} ${iconColor}"></i>
                            <span class="text-white">${message}</span>
                        </div>
                    </div>
                `;

                document.body.appendChild(notification);

                // Show notification
                requestAnimationFrame(() => {
                    notification.classList.add('show');
                });

                // Remove notification after delay
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            // Update the navbar scroll behavior
            document.addEventListener('DOMContentLoaded', () => {
                const navbar = document.querySelector('.navbar');

                window.addEventListener('scroll', () => {
                    if (window.scrollY > 20) {
                        navbar.classList.add('scrolled');
                    } else {
                        navbar.classList.remove('scrolled');
                    }
                });

                // Update all notification calls to use the new createNotification function
                const originalHandleLogout = handleLogout;
                handleLogout = async (event) => {
                    try {
                        await originalHandleLogout(event);
                        createNotification('Successfully logged out', 'success');
                    } catch (error) {
                        createNotification(error.message || 'Error during logout. Please try again.', 'error');
                    }
                };

                // Update login success notification
                const originalLoginFormSubmit = loginForm.onsubmit;
                loginForm.onsubmit = async (e) => {
                    try {
                        await originalLoginFormSubmit(e);
                        createNotification('Successfully logged in', 'success');
                    } catch (error) {
                        createNotification(error.message || 'Login failed. Please try again.', 'error');
                    }
                };
            });

            // Rotating Text Animation
            const rotatingTexts = [
                'College Experience',
                'Gaming Skills',
                'Competitive Edge',
                'Campus Life',
                'Academic Journey'
            ];
            let currentTextIndex = 0;
            const animatingTextSpan = document.getElementById('animatingText');
            const animationDuration = 500; // milliseconds

            function animateTextRotation() {
                if (!animatingTextSpan) return;

                // Add exit animation class
                animatingTextSpan.classList.add('text-exit');

                // Wait for exit animation to complete, then update text and animate entry
                setTimeout(() => {
                    currentTextIndex = (currentTextIndex + 1) % rotatingTexts.length;
                    animatingTextSpan.textContent = rotatingTexts[currentTextIndex];

                    // Remove exit and add entry animation class
                    animatingTextSpan.classList.remove('text-exit');
                    animatingTextSpan.classList.add('text-entry');

                    // Remove entry animation class after it completes
                    setTimeout(() => {
                        animatingTextSpan.classList.remove('text-entry');
                    }, animationDuration);

                }, animationDuration);
            }

            // Start the animation
            setInterval(animateTextRotation, 3000); // Change text every 3 seconds

            if (animatingTextSpan) {
                 animatingTextSpan.style.fontFamily = "'Orbitron', sans-serif";
                 animatingTextSpan.classList.add('font-loaded');
            }
        });
    </script>
</body>
</html>
