import { calculateDistance, isWithinRadius, isWithinCampus } from './coordinate.js';

class LocationManager {
    constructor() {
        this.watchId = null;
        this.lastPosition = null;
        this.rooms = new Map();
        this.nearbyPlayers = new Map();
        this.onLocationUpdate = null;
        this.onRoomEnter = null;
        this.onRoomExit = null;
        this.onPlayerNearby = null;
    }

    // Start tracking player location
    startTracking(callbacks) {
        this.onLocationUpdate = callbacks.onLocationUpdate;
        this.onRoomEnter = callbacks.onRoomEnter;
        this.onRoomExit = callbacks.onRoomExit;
        this.onPlayerNearby = callbacks.onPlayerNearby;

        if ('geolocation' in navigator) {
            this.watchId = navigator.geolocation.watchPosition(
                (position) => this.handlePositionUpdate(position),
                (error) => this.handleError(error),
                {
                    enableHighAccuracy: true,
                    timeout: 5000,
                    maximumAge: 0
                }
            );
        } else {
            throw new Error('Geolocation is not supported by this browser.');
        }
    }

    // Stop tracking player location
    stopTracking() {
        if (this.watchId !== null) {
            navigator.geolocation.clearWatch(this.watchId);
            this.watchId = null;
        }
    }

    // Handle position updates
    handlePositionUpdate(position) {
        const currentPosition = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
        };

        // Check if position has changed significantly
        if (this.hasPositionChanged(currentPosition)) {
            this.lastPosition = currentPosition;
            
            // Notify about location update
            if (this.onLocationUpdate) {
                this.onLocationUpdate(currentPosition);
            }

            // Check for room entry/exit
            this.checkRooms(currentPosition);
            
            // Check for nearby players
            this.checkNearbyPlayers(currentPosition);
        }
    }

    // Check if position has changed significantly (more than 2 meters)
    hasPositionChanged(newPosition) {
        if (!this.lastPosition) return true;
        
        const distance = calculateDistance(this.lastPosition, newPosition);
        return distance > 2; // 2 meters threshold
    }

    // Update room definitions
    updateRooms(roomsData) {
        roomsData.forEach(room => {
            this.rooms.set(room.id, room);
        });
        
        // Recheck rooms with new definitions
        if (this.lastPosition) {
            this.checkRooms(this.lastPosition);
        }
    }

    // Check if player has entered or exited any rooms
    checkRooms(position) {
        this.rooms.forEach(room => {
            const wasInRoom = room.isPlayerInside;
            const isInRoom = isWithinRadius(position, room.coordinates, room.radius);

            if (isInRoom && !wasInRoom) {
                room.isPlayerInside = true;
                if (this.onRoomEnter) {
                    this.onRoomEnter(room);
                }
            } else if (!isInRoom && wasInRoom) {
                room.isPlayerInside = false;
                if (this.onRoomExit) {
                    this.onRoomExit(room);
                }
            }
        });
    }

    // Update nearby players list
    updateNearbyPlayers(players) {
        this.nearbyPlayers.clear();
        players.forEach(player => {
            this.nearbyPlayers.set(player.id, player);
        });
        
        if (this.lastPosition) {
            this.checkNearbyPlayers(this.lastPosition);
        }
    }

    // Check for players within interaction range
    checkNearbyPlayers(position) {
        const INTERACTION_RANGE = 10; // 10 meters interaction range
        
        this.nearbyPlayers.forEach(player => {
            const wasNearby = player.isNearby;
            const isNearby = isWithinRadius(position, player.coordinates, INTERACTION_RANGE);

            if (isNearby && !wasNearby) {
                player.isNearby = true;
                if (this.onPlayerNearby) {
                    this.onPlayerNearby(player, true);
                }
            } else if (!isNearby && wasNearby) {
                player.isNearby = false;
                if (this.onPlayerNearby) {
                    this.onPlayerNearby(player, false);
                }
            }
        });
    }

    // Handle geolocation errors
    handleError(error) {
        console.error('Error getting location:', error);
        switch(error.code) {
            case error.PERMISSION_DENIED:
                throw new Error("Location permission denied");
            case error.POSITION_UNAVAILABLE:
                throw new Error("Location information unavailable");
            case error.TIMEOUT:
                throw new Error("Location request timed out");
            default:
                throw new Error("An unknown error occurred");
        }
    }
}

// Export LocationManager class
export default LocationManager;
